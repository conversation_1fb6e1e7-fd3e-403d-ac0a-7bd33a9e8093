import { LoginRequest, AuthResponse, ApiResponse, User, UserRole } from '../types/auth';

const API_BASE_URL = 'http://localhost:5000/api/v1';

// Mock users for demo purposes (when backend is not available)
const MOCK_USERS = [
  {
    _id: '1',
    username: 'admin',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: UserRole.ADMIN,
    department: 'IT',
    position: 'System Administrator',
    employeeCode: 'EMP001',
    gender: 'Nam' as const,
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '2',
    username: 'manager',
    email: '<EMAIL>',
    firstName: 'Manager',
    lastName: 'User',
    role: UserRole.MANAGER,
    department: 'Operations',
    position: 'Operations Manager',
    employeeCode: 'EMP002',
    gender: 'Nữ' as const,
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '3',
    username: 'employee',
    email: '<EMAIL>',
    firstName: 'Employee',
    lastName: 'User',
    role: UserRole.EMPLOYEE,
    department: 'Sales',
    position: 'Sales Representative',
    employeeCode: 'EMP003',
    gender: 'Nam' as const,
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '4',
    username: 'demo',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: UserRole.EMPLOYEE,
    department: 'General',
    position: 'Demo User',
    employeeCode: 'DEMO001',
    gender: 'Nam' as const,
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

// Mock credentials (email: password)
const MOCK_CREDENTIALS = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'manager123',
  '<EMAIL>': 'employee123',
  '<EMAIL>': 'demo123'
};

class AuthService {
  private static getHeaders(includeAuth = false): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (includeAuth) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  private static async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'An error occurred');
    }
    
    return data;
  }

  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      // Try backend first, if it fails, use mock data
      try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          headers: this.getHeaders(),
          body: JSON.stringify(credentials),
        });

        const result = await this.handleResponse<AuthResponse>(response);

        if (result.success && result.data) {
          // Store tokens in localStorage
          localStorage.setItem('accessToken', result.data.accessToken);
          localStorage.setItem('refreshToken', result.data.refreshToken);
          localStorage.setItem('user', JSON.stringify(result.data.user));

          return result.data;
        }

        throw new Error(result.message || 'Login failed');
      } catch (backendError) {
        console.warn('Backend not available, using mock authentication:', backendError);

        // Use mock authentication
        return this.mockLogin(credentials);
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  private static async mockLogin(credentials: LoginRequest): Promise<AuthResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check credentials
    const expectedPassword = MOCK_CREDENTIALS[credentials.email as keyof typeof MOCK_CREDENTIALS];

    if (!expectedPassword || expectedPassword !== credentials.password) {
      throw new Error('Email hoặc mật khẩu không đúng');
    }

    // Find user
    const user = MOCK_USERS.find(u => u.email === credentials.email);

    if (!user) {
      throw new Error('Không tìm thấy người dùng');
    }

    // Generate mock tokens
    const accessToken = `mock_access_token_${Date.now()}`;
    const refreshToken = `mock_refresh_token_${Date.now()}`;

    const authResponse: AuthResponse = {
      user,
      accessToken,
      refreshToken
    };

    // Store tokens in localStorage
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('user', JSON.stringify(user));

    return authResponse;
  }



  static async logout(): Promise<void> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (refreshToken) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: this.getHeaders(true),
          body: JSON.stringify({ refreshToken }),
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  }

  static async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (!refreshToken) {
        return null;
      }

      const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ refreshToken }),
      });

      const result = await this.handleResponse<{ accessToken: string; refreshToken: string }>(response);
      
      if (result.success && result.data) {
        localStorage.setItem('accessToken', result.data.accessToken);
        localStorage.setItem('refreshToken', result.data.refreshToken);
        return result.data.accessToken;
      }
      
      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.logout(); // Clear invalid tokens
      return null;
    }
  }

  static getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  static getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken() && !!this.getCurrentUser();
  }
}

export default AuthService;
