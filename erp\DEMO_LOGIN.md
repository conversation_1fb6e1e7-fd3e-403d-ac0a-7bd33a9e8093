# Demo Login Instructions

## T<PERSON><PERSON> khoản demo để đăng nhập

Hệ thống đã được cấu hình để hoạt động mà không cần backend. Bạn có thể sử dụng các tài khoản demo sau:

### 1. Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: Administrator
- **Department**: IT
- **Position**: System Administrator

### 2. Manager Account
- **Email**: `<EMAIL>`
- **Password**: `manager123`
- **Role**: Manager
- **Department**: Operations
- **Position**: Operations Manager

### 3. Employee Account
- **Email**: `<EMAIL>`
- **Password**: `employee123`
- **Role**: Employee
- **Department**: Sales
- **Position**: Sales Representative

### 4. Demo Account
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Role**: Employee (default)

## Cách sử dụng

1. Mở ứng dụng và truy cập trang đăng nhập
2. Nhập một trong các email và password ở trên
3. Click "Đăng nhập"
4. Hệ thống sẽ tự động đăng nhập và chuyển đến dashboard

## Lưu ý

- Hệ thống sẽ tự động thử kết nối backend trước, nếu không thành công sẽ sử dụng mock data
- Thông tin đăng nhập sẽ được lưu trong localStorage
- Để đăng xuất, sử dụng nút logout trong giao diện
- Mock authentication có delay 1 giây để mô phỏng thời gian phản hồi thực tế

## Tính năng

- ✅ Login với mock users
- ✅ Lưu trữ session trong localStorage
- ✅ Protected routes
- ✅ Auto redirect sau khi login
- ✅ Logout functionality
- ✅ Role-based access (admin, manager, employee)

## Troubleshooting

Nếu gặp lỗi khi đăng nhập:
1. Kiểm tra lại email và password
2. Mở Developer Tools (F12) để xem console logs
3. Xóa localStorage và thử lại: `localStorage.clear()`
